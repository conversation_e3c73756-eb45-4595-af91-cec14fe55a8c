import React, { useState } from 'react';
import Card from '../ui/Card';
import Button from '../ui/Button';
import toast from 'react-hot-toast';

interface MockDevice {
  id: string;
  name: string;
  type: 'printer' | 'scanner';
  status: 'connected' | 'disconnected';
}

const HardwareSettings: React.FC = () => {
  const [selectedPrinter, setSelectedPrinter] = useState<string>('');
  const [selectedScanner, setSelectedScanner] = useState<string>('');

  // Mock devices for demonstration
  const mockPrinters: MockDevice[] = [
    { id: 'katasymbol-t50m', name: 'Katasymbol T50M Pro Label Printer', type: 'printer', status: 'connected' },
    { id: 'generic-receipt', name: 'Generic Receipt Printer', type: 'printer', status: 'disconnected' },
  ];

  const mockScanners: MockDevice[] = [
    { id: 'inateck-bcst21', name: 'Inateck BCST-21-AI Scanner', type: 'scanner', status: 'connected' },
    { id: 'generic-scanner', name: 'Generic Barcode Scanner', type: 'scanner', status: 'disconnected' },
  ];

  const handleTestPrint = () => {
    if (!selectedPrinter) {
      toast.error('Please select a printer first');
      return;
    }

    const printer = mockPrinters.find(p => p.id === selectedPrinter);
    toast.success(`Test print sent to ${printer?.name}! (Simulated)`);
  };

  const handleTestScan = () => {
    if (!selectedScanner) {
      toast.error('Please select a scanner first');
      return;
    }

    const scanner = mockScanners.find(s => s.id === selectedScanner);
    toast.success(`Scanner test completed for ${scanner?.name}! (Simulated)`);
  };

  const handlePrintBarcode = (productName: string) => {
    if (!selectedPrinter) {
      toast.error('Please select a printer first');
      return;
    }

    toast.success(`Barcode for "${productName}" would be printed! (Simulated)`);
  };

  return (
    <div className="space-y-6">
      {/* Label Printer Settings */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Label Printer</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Printer
              </label>
              <select
                value={selectedPrinter}
                onChange={(e) => setSelectedPrinter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a printer...</option>
                {mockPrinters.map((printer) => (
                  <option key={printer.id} value={printer.id}>
                    {printer.name} ({printer.status})
                  </option>
                ))}
              </select>
            </div>

            <div className="flex space-x-3">
              <Button
                onClick={handleTestPrint}
                disabled={!selectedPrinter}
                variant="outline"
              >
                Test Print
              </Button>
              
              <Button
                onClick={() => handlePrintBarcode('Sample Product')}
                disabled={!selectedPrinter}
                variant="outline"
              >
                Print Sample Barcode
              </Button>
            </div>

            {selectedPrinter && (
              <div className="mt-4 p-3 bg-blue-50 rounded-md">
                <p className="text-sm text-blue-700">
                  <strong>Note:</strong> This is a mock implementation. In production, this would connect to the actual {mockPrinters.find(p => p.id === selectedPrinter)?.name} via Bluetooth.
                </p>
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Receipt Printer Settings */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Receipt Printer</h3>
          
          <div className="space-y-4">
            <div className="p-4 bg-green-50 rounded-md">
              <h4 className="font-medium text-green-800 mb-2">Standard OS Print</h4>
              <p className="text-sm text-green-700">
                Receipts are printed using the standard browser print dialog (window.print()). 
                This allows users to select any printer available on their system.
              </p>
            </div>

            <Button
              onClick={() => {
                // Simulate receipt printing
                const receiptContent = `
                  <div style="font-family: monospace; width: 300px; margin: 0 auto;">
                    <h2 style="text-align: center;">ISMS Store</h2>
                    <p style="text-align: center;">Sample Receipt</p>
                    <hr>
                    <p>Date: ${new Date().toLocaleDateString()}</p>
                    <p>Time: ${new Date().toLocaleTimeString()}</p>
                    <hr>
                    <p>Sample Item 1 ............ $10.00</p>
                    <p>Sample Item 2 ............ $15.00</p>
                    <hr>
                    <p><strong>Total: $25.00</strong></p>
                    <p style="text-align: center;">Thank you!</p>
                  </div>
                `;
                
                const printWindow = window.open('', '_blank');
                if (printWindow) {
                  printWindow.document.write(receiptContent);
                  printWindow.document.close();
                  printWindow.print();
                  printWindow.close();
                }
                
                toast.success('Receipt print dialog opened!');
              }}
              variant="outline"
            >
              Test Receipt Print
            </Button>
          </div>
        </div>
      </Card>

      {/* Barcode Scanner Settings */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Barcode Scanner</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Scanner
              </label>
              <select
                value={selectedScanner}
                onChange={(e) => setSelectedScanner(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a scanner...</option>
                {mockScanners.map((scanner) => (
                  <option key={scanner.id} value={scanner.id}>
                    {scanner.name} ({scanner.status})
                  </option>
                ))}
              </select>
            </div>

            <Button
              onClick={handleTestScan}
              disabled={!selectedScanner}
              variant="outline"
            >
              Test Scanner
            </Button>

            {selectedScanner && (
              <div className="mt-4 p-3 bg-green-50 rounded-md">
                <p className="text-sm text-green-700">
                  <strong>HID Keyboard Emulation:</strong> The scanner works as a keyboard input device. 
                  Scanned barcodes are automatically captured by the global keyboard listener in the POS interface.
                </p>
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Hardware Status Overview */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Hardware Status</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Label Printer</h4>
              <p className="text-sm text-gray-600">
                {selectedPrinter ? 
                  `Connected: ${mockPrinters.find(p => p.id === selectedPrinter)?.name}` : 
                  'No printer selected'
                }
              </p>
            </div>
            
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Barcode Scanner</h4>
              <p className="text-sm text-gray-600">
                {selectedScanner ? 
                  `Connected: ${mockScanners.find(s => s.id === selectedScanner)?.name}` : 
                  'No scanner selected'
                }
              </p>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default HardwareSettings;
