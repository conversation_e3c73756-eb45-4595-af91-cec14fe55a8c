import { publicInvoke, secureInvoke } from '../utils/apiInterceptor';
import { onlineApiService } from './onlineApiService';
import { LoginResponse, User } from '../types';
import { useAuthStore } from '../store/authStore';

export const authService = {
  // Initialize authentication state on app startup
  initializeAuth: async (): Promise<void> => {
    const { authToken, setLoading, logout, login } = useAuthStore.getState();

    if (!authToken) {
      setLoading(false);
      return;
    }

    setLoading(true);

    try {
      // Validate the stored token
      const isValid = await authService.validateSession();

      if (isValid) {
        // Get current user info to refresh the auth state
        const user = await authService.getCurrentUser();
        // The token is already in the store, just update user info
        login(authToken, user);
      } else {
        // Token is invalid, clear auth state
        logout();
      }
    } catch (error) {
      console.error('Auth initialization failed:', error);
      logout();
    } finally {
      setLoading(false);
    }
  },
  // Login using online API
  login: async (email: string, password: string): Promise<LoginResponse> => {
    try {
      // First try online API login
      const response = await onlineApiService.post<LoginResponse>('/auth/login/', {
        email,
        password
      });

      // If successful, save user data to local database for offline access
      if (response.token && response.user) {
        await authService.saveUserToLocal(response.user);
      }

      return response;
    } catch (error) {
      // If online login fails, fall back to local authentication
      console.warn('Online login failed, trying local authentication:', error);
      return await publicInvoke('login', { email, password });
    }
  },

  // Save user data to local database after successful online login
  saveUserToLocal: async (user: User): Promise<void> => {
    try {
      await publicInvoke('save_user_to_local', { user });
    } catch (error) {
      console.error('Failed to save user to local database:', error);
      // Don't throw error - this is not critical for login success
    }
  },

  // Logout (requires token)
  logout: async (): Promise<string> => {
    return await secureInvoke('logout');
  },

  // Get current user - try online first, fallback to local
  getCurrentUser: async (): Promise<User> => {
    try {
      // Try online API first
      return await onlineApiService.get<User>('/auth/me/');
    } catch (error) {
      console.warn('Online user fetch failed, trying local:', error);
      // Fallback to local database
      return await secureInvoke('get_current_user');
    }
  },

  // Validate session - try online first, fallback to local
  validateSession: async (): Promise<boolean> => {
    try {
      // Try online API first
      await onlineApiService.get('/auth/me/');
      return true;
    } catch (error) {
      console.warn('Online session validation failed, trying local:', error);
      // Fallback to local validation
      try {
        await secureInvoke('validate_user_session');
        return true;
      } catch (localError) {
        console.error('Local session validation also failed:', localError);
        return false;
      }
    }
  },

  // Check user permission (requires token)
  checkPermission: async (permission: string): Promise<boolean> => {
    try {
      const hasPermission = await secureInvoke<boolean>('check_user_permission', { permission });
      return hasPermission;
    } catch (error) {
      console.error('Permission check failed:', error);
      return false;
    }
  },
};
