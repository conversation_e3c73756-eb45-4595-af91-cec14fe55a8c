import { publicInvoke, secureInvoke } from '../utils/apiInterceptor';
import { LoginResponse, User } from '../types';
import { useAuthStore } from '../store/authStore';

export const authService = {
  // Initialize authentication state on app startup
  initializeAuth: async (): Promise<void> => {
    const { authToken, setLoading, logout, login } = useAuthStore.getState();

    if (!authToken) {
      setLoading(false);
      return;
    }

    setLoading(true);

    try {
      // Validate the stored token
      const isValid = await authService.validateSession();

      if (isValid) {
        // Get current user info to refresh the auth state
        const user = await authService.getCurrentUser();
        // The token is already in the store, just update user info
        login(authToken, user);
      } else {
        // Token is invalid, clear auth state
        logout();
      }
    } catch (error) {
      console.error('Auth initialization failed:', error);
      logout();
    } finally {
      setLoading(false);
    }
  },
  // Login (public - no token required)
  login: async (email: string, password: string): Promise<LoginResponse> => {
    return await publicInvoke('login', { email, password });
  },

  // Logout (requires token)
  logout: async (): Promise<string> => {
    return await secureInvoke('logout');
  },

  // Get current user (requires token)
  getCurrentUser: async (): Promise<User> => {
    return await secureInvoke('get_current_user');
  },

  // Validate session (requires token)
  validateSession: async (): Promise<boolean> => {
    try {
      await secureInvoke('validate_user_session');
      return true;
    } catch (error) {
      console.error('Session validation failed:', error);
      return false;
    }
  },

  // Check user permission (requires token)
  checkPermission: async (permission: string): Promise<boolean> => {
    try {
      const hasPermission = await secureInvoke<boolean>('check_user_permission', { permission });
      return hasPermission;
    } catch (error) {
      console.error('Permission check failed:', error);
      return false;
    }
  },
};
