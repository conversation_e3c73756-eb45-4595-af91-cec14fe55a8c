import { secureInvoke } from '../utils/apiInterceptor';
import { roleService } from './roleService';
import { User, Role, Permission } from '../types';

export const userService = {
  // Get all users - use local for now since no API endpoint exists
  getUsers: async (): Promise<User[]> => {
    try {
      return await secureInvoke('get_users');
    } catch (error) {
      console.warn('Failed to get users:', error);
      return [];
    }
  },

  // Create a new user
  createUser: async (userData: {
    email: string;
    fullName: string;
    role: string;
    password: string;
  }): Promise<number> => {
    return await secureInvoke('create_user', {
      email: userData.email,
      fullName: userData.fullName,
      role: userData.role,
      password: userData.password,
    });
  },

  // Update an existing user
  updateUser: async (
    userId: number,
    userData: {
      email: string;
      fullName: string;
      role: string;
    }
  ): Promise<void> => {
    return await secureInvoke('update_user', {
      userId,
      email: userData.email,
      fullName: userData.fullName,
      role: userData.role,
    });
  },

  // Delete a user
  deleteUser: async (userId: number): Promise<void> => {
    return await secureInvoke('delete_user', { userId });
  },

  // Get all roles - use role service
  getRoles: async (): Promise<Role[]> => {
    try {
      const rolesData = await roleService.getAllRoles();
      // Convert to Role[] format expected by UI
      return Object.keys(rolesData).map((name, index) => ({
        id: index + 1,
        name,
        description: name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        created_at: new Date().toISOString()
      }));
    } catch (error) {
      console.warn('Failed to get roles from API, using local fallback:', error);
      // Fallback to local
      try {
        return await secureInvoke('get_roles');
      } catch (localError) {
        console.warn('Local roles also failed, using defaults:', localError);
        return [
          { id: 1, name: 'admin', description: 'Administrator', created_at: new Date().toISOString() },
          { id: 2, name: 'manager', description: 'Manager', created_at: new Date().toISOString() },
          { id: 3, name: 'cashier', description: 'Cashier', created_at: new Date().toISOString() }
        ];
      }
    }
  },

  // Get all permissions - use role service
  getPermissions: async (): Promise<Permission[]> => {
    try {
      const permissions = await roleService.getAllPermissions();
      // Convert to Permission[] format expected by UI
      return permissions.map((name, index) => ({
        id: index + 1,
        name,
        description: name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        created_at: new Date().toISOString()
      }));
    } catch (error) {
      console.warn('Failed to get permissions from API, using local fallback:', error);
      // Fallback to local
      try {
        return await secureInvoke('get_permissions');
      } catch (localError) {
        console.warn('Local permissions also failed, using defaults:', localError);
        return [
          { id: 1, name: 'user_management', description: 'User Management', created_at: new Date().toISOString() },
          { id: 2, name: 'inventory_management', description: 'Inventory Management', created_at: new Date().toISOString() },
          { id: 3, name: 'sales_management', description: 'Sales Management', created_at: new Date().toISOString() },
          { id: 4, name: 'reporting', description: 'Reporting', created_at: new Date().toISOString() },
          { id: 5, name: 'system_settings', description: 'System Settings', created_at: new Date().toISOString() },
          { id: 6, name: 'dashboard_access', description: 'Dashboard Access', created_at: new Date().toISOString() }
        ];
      }
    }
  },
};
