import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import { useAuthStore } from '../store/authStore';

/**
 * Online API Service for direct frontend calls to the online backend
 * Handles authentication, request construction, and error handling
 */
class OnlineApiService {
  private client: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = 'http://localhost:8000/api/v1';
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const { authToken } = useAuthStore.getState();
        if (authToken) {
          config.headers.Authorization = `Bearer ${authToken}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle auth errors
    this.client.interceptors.response.use(
      (response) => response,
      (error: AxiosError) => {
        if (error.response?.status === 401 || error.response?.status === 403) {
          // Invalid or expired token - trigger logout
          useAuthStore.getState().logout();
          // Redirect to login will be handled by the auth store or route guards
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Check if the online API is reachable
   */
  async checkConnectivity(): Promise<boolean> {
    try {
      // Try to access the auth/me endpoint as a connectivity test
      // This is more reliable than a health endpoint that might not exist
      const response = await this.client.get('/auth/me/', {
        timeout: 5000,
        validateStatus: (status) => status < 500 // Accept 401/403 as "connected"
      });
      return response.status < 500;
    } catch (error) {
      return false;
    }
  }

  /**
   * Generic GET request
   */
  async get<T>(endpoint: string, params?: any): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.get(endpoint, { params });
      return response.data;
    } catch (error) {
      this.handleError(error as AxiosError);
      throw error;
    }
  }

  /**
   * Generic POST request
   */
  async post<T>(endpoint: string, data?: any): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.post(endpoint, data);
      return response.data;
    } catch (error) {
      this.handleError(error as AxiosError);
      throw error;
    }
  }

  /**
   * Generic PUT request
   */
  async put<T>(endpoint: string, data?: any): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.put(endpoint, data);
      return response.data;
    } catch (error) {
      this.handleError(error as AxiosError);
      throw error;
    }
  }

  /**
   * Generic DELETE request
   */
  async delete<T>(endpoint: string): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.delete(endpoint);
      return response.data;
    } catch (error) {
      this.handleError(error as AxiosError);
      throw error;
    }
  }

  /**
   * Handle API errors and determine if they're network-related
   */
  private handleError(error: AxiosError): void {
    if (error.code === 'ECONNREFUSED' || 
        error.code === 'ENOTFOUND' || 
        error.code === 'ECONNABORTED' ||
        !error.response) {
      // Network error - should trigger offline fallback
      console.warn('Network error detected:', error.message);
      throw new NetworkError(error.message);
    } else {
      // API error - should not trigger offline fallback
      console.error('API error:', error.response?.status, error.response?.data);
      throw new ApiError(
        error.response?.status || 500,
        (error.response?.data as any)?.message || error.message
      );
    }
  }

  /**
   * User-related API calls
   */
  users = {
    getAll: () => this.get<any[]>('/users/'),
    getById: (id: number) => this.get<any>(`/users/${id}/`),
    create: (userData: any) => this.post<any>('/users/', userData),
    update: (id: number, userData: any) => this.put<any>(`/users/${id}/`, userData),
    delete: (id: number) => this.delete<void>(`/users/${id}/`),
  };

  /**
   * Product-related API calls
   */
  products = {
    getAll: () => this.get<any[]>('/inventory/products/'),
    getById: (id: number) => this.get<any>(`/inventory/products/${id}/`),
    getBySku: (sku: string) => this.get<any>(`/inventory/products/sku/${sku}/`),
    create: (productData: any) => this.post<any>('/inventory/products/', productData),
    update: (id: number, productData: any) => this.put<any>(`/inventory/products/${id}/`, productData),
    delete: (id: number) => this.delete<void>(`/inventory/products/${id}/`),
    getLowStock: () => this.get<any[]>('/inventory/products/low-stock/'),
    updateStock: (id: number, stockData: any) => this.put<any>(`/inventory/products/${id}/stock/`, stockData),
  };

  /**
   * Supplier-related API calls
   */
  suppliers = {
    getAll: () => this.get<any[]>('/inventory/suppliers/'),
    getById: (id: number) => this.get<any>(`/inventory/suppliers/${id}/`),
    create: (supplierData: any) => this.post<any>('/inventory/suppliers/', supplierData),
    update: (id: number, supplierData: any) => this.put<any>(`/inventory/suppliers/${id}/`, supplierData),
    delete: (id: number) => this.delete<void>(`/inventory/suppliers/${id}/`),
  };

  /**
   * Sales/Order-related API calls
   */
  orders = {
    getAll: () => this.get<any[]>('/sales/orders/'),
    getRecent: (limit: number = 10) => this.get<any[]>(`/sales/orders/?limit=${limit}`),
    getById: (id: number) => this.get<any>(`/sales/orders/${id}/`),
    create: (orderData: any) => this.post<any>('/sales/orders/', orderData),
    update: (id: number, orderData: any) => this.put<any>(`/sales/orders/${id}/`, orderData),
    complete: (id: number) => this.post<any>(`/sales/orders/${id}/complete/`, {}),
    cancel: (id: number) => this.post<any>(`/sales/orders/${id}/cancel/`, {}),
    refund: (id: number) => this.post<any>(`/sales/orders/${id}/refund/`, {}),
  };

  /**
   * Sales Reports API calls
   */
  salesReports = {
    generateSalesReport: (data: { start_date: string; end_date: string }) =>
      this.post<any>('/sales/reports/sales/', data),
    getDailySales: () => this.get<any>('/sales/reports/daily-sales/'),
  };

  /**
   * Stock movement-related API calls
   */
  stockMovements = {
    getAll: () => this.get<any[]>('/stock-movements/'),
    getById: (id: number) => this.get<any>(`/stock-movements/${id}/`),
    create: (movementData: any) => this.post<any>('/stock-movements/', movementData),
  };
}

/**
 * Custom error classes for better error handling
 */
export class NetworkError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'NetworkError';
  }
}

export class ApiError extends Error {
  public status: number;

  constructor(status: number, message: string) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
  }
}

/**
 * Utility function to check if an error is network-related
 */
export function isNetworkError(error: any): error is NetworkError {
  return error instanceof NetworkError;
}

/**
 * Utility function to check if an error is API-related
 */
export function isApiError(error: any): error is ApiError {
  return error instanceof ApiError;
}

// Export singleton instance
export const onlineApiService = new OnlineApiService();
export default onlineApiService;
